import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Tenant from './tenant.js'
import PricingPlan from './pricing_plan.js'
import { PlanLimitationType } from '#enums/plan_limitation'
import { getPlanLimitation, isUnlimited, BillingPlan } from '#constants/plan_limitations'

export enum BillingStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  PAST_DUE = 'past_due',
}

export enum BillingCycle {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export default class Billing extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare tenantId: number

  @column()
  declare plan: BillingPlan

  @column()
  declare status: BillingStatus

  @column()
  declare cycle: BillingCycle

  @column()
  declare amount: number // Amount in cents

  @column()
  declare currency: string

  @column()
  declare stripeCustomerId: string | null

  @column()
  declare stripeSubscriptionId: string | null

  @column()
  declare stripePriceId: string | null

  @column.dateTime()
  declare currentPeriodStart: DateTime | null

  @column.dateTime()
  declare currentPeriodEnd: DateTime | null

  @column.dateTime()
  declare trialStart: DateTime | null

  @column.dateTime()
  declare trialEnd: DateTime | null

  @column.dateTime()
  declare cancelledAt: DateTime | null

  @column()
  declare cancelAtPeriodEnd: boolean

  @column()
  declare maxUsers: number | null

  @column()
  declare maxStorage: number | null // Storage in GB

  @column()
  declare features: string | null // JSON string of enabled features

  @column()
  declare needsUpgrade: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => Tenant)
  declare tenant: BelongsTo<typeof Tenant>

  @belongsTo(() => PricingPlan, {
    foreignKey: 'plan',
    localKey: 'slug',
  })
  declare pricingPlan: BelongsTo<typeof PricingPlan>

  // Helper methods
  get isActive(): boolean {
    return this.status === BillingStatus.ACTIVE
  }

  get isTrialing(): boolean {
    if (!this.trialEnd) return false
    return DateTime.now() < this.trialEnd
  }

  get isPastDue(): boolean {
    return this.status === BillingStatus.PAST_DUE
  }

  get isCancelled(): boolean {
    return this.status === BillingStatus.CANCELLED
  }

  get daysUntilTrialEnd(): number | null {
    if (!this.trialEnd) return null
    const diff = this.trialEnd.diff(DateTime.now(), 'days')
    return Math.max(0, Math.floor(diff.days))
  }

  get daysUntilPeriodEnd(): number | null {
    if (!this.currentPeriodEnd) return null
    const diff = this.currentPeriodEnd.diff(DateTime.now(), 'days')
    return Math.max(0, Math.floor(diff.days))
  }

  get featuresArray(): string[] {
    if (!this.features) return []
    try {
      return JSON.parse(this.features)
    } catch {
      return []
    }
  }

  set featuresArray(features: string[]) {
    this.features = JSON.stringify(features)
  }

  hasFeature(feature: string): boolean {
    return this.featuresArray.includes(feature)
  }

  // Plan-specific feature checks
  get canCreateUnlimitedUsers(): boolean {
    return this.plan === BillingPlan.ENTERPRISE || this.maxUsers === null
  }

  get canUseAdvancedAnalytics(): boolean {
    return [BillingPlan.PRO, BillingPlan.ENTERPRISE].includes(this.plan)
  }

  get canUseCustomBranding(): boolean {
    return this.plan === BillingPlan.ENTERPRISE
  }

  get canUseAPIAccess(): boolean {
    return [BillingPlan.PRO, BillingPlan.ENTERPRISE].includes(this.plan)
  }

  // Plan limitation helper methods
  /**
   * Get plan limitation for a specific limitation type
   */
  getPlanLimit(limitationType: PlanLimitationType): number | null {
    return getPlanLimitation(this.plan, limitationType)
  }

  /**
   * Check if plan has unlimited access for a specific limitation type
   */
  hasUnlimitedAccess(limitationType: PlanLimitationType): boolean {
    return isUnlimited(this.plan, limitationType)
  }

  /**
   * Check if current usage would exceed plan limit
   */
  wouldExceedLimit(
    limitationType: PlanLimitationType,
    currentUsage: number,
    additionalUsage: number = 1
  ): boolean {
    if (this.hasUnlimitedAccess(limitationType)) {
      return false
    }

    const limit = this.getPlanLimit(limitationType)
    if (limit === null) {
      return false
    }

    return currentUsage + additionalUsage > limit
  }

  /**
   * Get remaining capacity for a specific limitation type
   */
  getRemainingCapacity(limitationType: PlanLimitationType, currentUsage: number): number | null {
    if (this.hasUnlimitedAccess(limitationType)) {
      return null // Unlimited
    }

    const limit = this.getPlanLimit(limitationType)
    if (limit === null) {
      return null // Unlimited
    }

    return Math.max(0, limit - currentUsage)
  }

  /**
   * Get usage percentage for a specific limitation type
   */
  getUsagePercentage(limitationType: PlanLimitationType, currentUsage: number): number | null {
    if (this.hasUnlimitedAccess(limitationType)) {
      return null // Unlimited
    }

    const limit = this.getPlanLimit(limitationType)
    if (limit === null || limit === 0) {
      return null
    }

    return Math.min(100, (currentUsage / limit) * 100)
  }

  /**
   * Check if usage is approaching limit (>= 80%)
   */
  isApproachingLimit(limitationType: PlanLimitationType, currentUsage: number): boolean {
    const percentage = this.getUsagePercentage(limitationType, currentUsage)
    return percentage !== null && percentage >= 80
  }

  /**
   * Check if usage has exceeded limit
   */
  hasExceededLimit(limitationType: PlanLimitationType, currentUsage: number): boolean {
    const percentage = this.getUsagePercentage(limitationType, currentUsage)
    return percentage !== null && percentage >= 100
  }
}
