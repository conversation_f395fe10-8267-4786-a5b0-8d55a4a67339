import { inject } from '@adonisjs/core'
import Billing, { BillingStatus, BillingCycle } from '#models/billing'
import { BillingPlan } from '#constants/plan_limitations'
import BillingRepository from '#repositories/billing_repository'
import { DateTime } from 'luxon'

import BaseService from './base_service.js'
import StripeService from './stripe_service.js'
import User from '#models/user'

export interface BillingFilters {
  status?: BillingStatus
  plan?: BillingPlan
  cycle?: BillingCycle
  search?: string
}

export interface CreateBillingRequest {
  tenantId: number
  plan: BillingPlan
  cycle: BillingCycle
  amount?: number
  currency?: string
  maxUsers?: number
  maxStorage?: number
  features?: string[]
  trialDays?: number
  stripeCustomerId?: string
}

export interface UpdateBillingRequest {
  plan?: BillingPlan
  cycle?: BillingCycle
  status?: BillingStatus
  amount?: number
  currency?: string
  maxUsers?: number
  maxStorage?: number
  features?: string[]
  stripeCustomerId?: string
  stripeSubscriptionId?: string
  stripePriceId?: string
  currentPeriodStart?: DateTime
  currentPeriodEnd?: DateTime
  cancelledAt?: DateTime
}

@inject()
export default class BillingService extends BaseService<typeof Billing> {
  protected model = Billing
  protected repository: BillingRepository

  constructor(
    billingRepository: BillingRepository,
    protected stripeService: StripeService
  ) {
    super()
    this.repository = billingRepository
  }

  private get billingRepository() {
    return this.repository
  }

  /**
   * Find billing record by ID or throw error
   */
  async findOrFail(id: number): Promise<Billing> {
    return await this.billingRepository.findByIdOrFail(id)
  }

  /**
   * Delete billing record
   */
  async delete(id: number): Promise<void> {
    return await this.billingRepository.delete(id)
  }

  /**
   * Create billing record for a tenant
   */
  async createBilling(data: CreateBillingRequest, user?: User): Promise<Billing> {
    let stripeCustomerId = data.stripeCustomerId

    // Create Stripe customer if user is provided and no customer ID exists
    if (user && !stripeCustomerId) {
      const stripeCustomer = await this.stripeService.createCustomer(user.email)
      stripeCustomerId = stripeCustomer.id
    }

    const billingData: Partial<Billing> = {
      stripeCustomerId,
      tenantId: data.tenantId,
      plan: data.plan,
      cycle: data.cycle,
      status: BillingStatus.ACTIVE,
      amount: data.amount || this.getDefaultAmount(data.plan, data.cycle),
      currency: data.currency || 'usd',
      maxUsers: data.maxUsers || this.getDefaultMaxUsers(data.plan),
      maxStorage: data.maxStorage || this.getDefaultMaxStorage(data.plan),
      features: data.features
        ? JSON.stringify(data.features)
        : JSON.stringify(this.getDefaultFeatures(data.plan)),
      cancelAtPeriodEnd: false,
    }

    // Set trial period if specified
    if (data.trialDays && data.trialDays > 0) {
      billingData.trialStart = DateTime.now()
      billingData.trialEnd = DateTime.now().plus({ days: data.trialDays })
    }

    // Set current period
    billingData.currentPeriodStart = DateTime.now()
    billingData.currentPeriodEnd = this.calculatePeriodEnd(data.cycle)

    // Use repository to create the billing record
    const billing = await this.billingRepository.create(billingData)

    // Calculate and set needsUpgrade field
    await this.updateNeedsUpgrade(billing)

    return billing
  }

  /**
   * Update billing information
   */
  async updateBilling(id: number, data: UpdateBillingRequest): Promise<Billing> {
    // Update the billing record directly
    const updatedBilling = await this.billingRepository.update(id, data)

    // Recalculate needsUpgrade field if needed
    if (data.plan || data.cycle || data.status) {
      await this.updateNeedsUpgrade(updatedBilling)
    }

    return updatedBilling
  }

  /**
   * Cancel billing subscription
   */
  async cancelBilling(id: number, cancelAtPeriodEnd: boolean = true): Promise<Billing> {
    // Verify billing exists using repository
    await this.billingRepository.findByIdOrFail(id)

    const updateData: Partial<Billing> = {
      cancelAtPeriodEnd,
    }

    if (!cancelAtPeriodEnd) {
      updateData.status = BillingStatus.CANCELLED
      updateData.cancelledAt = DateTime.now()
    }

    return await this.billingRepository.update(id, updateData)
  }

  /**
   * Suspend billing (for non-payment)
   */
  async suspendBilling(id: number): Promise<Billing> {
    return await this.billingRepository.update(id, {
      status: BillingStatus.SUSPENDED,
    })
  }

  /**
   * Reactivate billing
   */
  async reactivateBilling(id: number): Promise<Billing> {
    return await this.billingRepository.update(id, {
      status: BillingStatus.ACTIVE,
      cancelledAt: null,
      cancelAtPeriodEnd: false,
    })
  }

  /**
   * Get billing by tenant ID
   */
  async getByTenantId(tenantId: number): Promise<Billing | null> {
    const billing = await this.billingRepository.findByTenantId(tenantId)

    // Update needsUpgrade field if billing exists
    if (billing) {
      await this.updateNeedsUpgrade(billing)
    }

    return billing
  }

  /**
   * Calculate and update the needsUpgrade field for a billing record
   */
  async updateNeedsUpgrade(billing: Billing): Promise<void> {
    const needsUpgrade = this.calculateNeedsUpgrade(billing)

    if (billing.needsUpgrade !== needsUpgrade) {
      await this.billingRepository.updateNeedsUpgrade(billing.id, needsUpgrade)
      // Update the local instance as well
      billing.needsUpgrade = needsUpgrade
    }
  }

  /**
   * Calculate if billing needs upgrade based on business logic
   */
  private calculateNeedsUpgrade(billing: Billing): boolean {
    // No billing record means needs setup
    if (!billing) return true

    // Check various conditions that require attention
    const isActive = billing.status === BillingStatus.ACTIVE
    const isTrialExpired = billing.trialEnd && billing.trialEnd < DateTime.now()
    const isPastDue = billing.status === BillingStatus.PAST_DUE
    const isCancelled = billing.status === BillingStatus.CANCELLED
    const isFree = billing.plan === BillingPlan.FREE

    // Show upgrade banner for any of these conditions
    return !isActive || !!isTrialExpired || isPastDue || isCancelled || isFree
  }

  /**
   * Check if tenant has active billing
   */
  async hasActiveBilling(tenantId: number): Promise<boolean> {
    const billing = await this.getByTenantId(tenantId)
    return billing?.isActive || false
  }

  /**
   * Get expiring trials (within specified days)
   */
  async getExpiringTrials(days: number = 7): Promise<Billing[]> {
    return await this.billingRepository.findExpiringTrials(days)
  }

  /**
   * Get overdue subscriptions
   */
  async getOverdueSubscriptions(): Promise<Billing[]> {
    // Use repository query builder for complex queries
    return await this.billingRepository
      .getQueryBuilder()
      .where('status', BillingStatus.PAST_DUE)
      .orWhere(function (query) {
        query
          .where('status', BillingStatus.ACTIVE)
          .whereNotNull('currentPeriodEnd')
          .where('currentPeriodEnd', '<', DateTime.now().toSQL())
      })
  }

  // Helper methods
  private getDefaultAmount(plan: BillingPlan, cycle: BillingCycle): number {
    const prices = {
      [BillingPlan.FREE]: { monthly: 0, yearly: 0 },
      [BillingPlan.BASIC]: { monthly: 2900, yearly: 29000 }, // $29/month, $290/year
      [BillingPlan.PRO]: { monthly: 9900, yearly: 99000 }, // $99/month, $990/year
      [BillingPlan.ENTERPRISE]: { monthly: 29900, yearly: 299000 }, // $299/month, $2990/year
    }

    return prices[plan][cycle === BillingCycle.YEARLY ? 'yearly' : 'monthly']
  }

  private getDefaultMaxUsers(plan: BillingPlan): number | null {
    // For loyalty systems, this represents max customers
    const limits = {
      [BillingPlan.FREE]: 100,
      [BillingPlan.BASIC]: 1000,
      [BillingPlan.PRO]: 10000,
      [BillingPlan.ENTERPRISE]: null, // Unlimited
    }

    return limits[plan]
  }

  private getDefaultMaxStorage(plan: BillingPlan): number | null {
    // For loyalty systems, this could represent data storage for customer profiles
    const limits = {
      [BillingPlan.FREE]: 1, // 1GB
      [BillingPlan.BASIC]: 5, // 5GB
      [BillingPlan.PRO]: 25, // 25GB
      [BillingPlan.ENTERPRISE]: null, // Unlimited
    }

    return limits[plan]
  }

  private getDefaultFeatures(plan: BillingPlan): string[] {
    const features = {
      [BillingPlan.FREE]: [
        'basic_loyalty',
        'customer_management',
        'mobile_app',
        'basic_analytics',
        'email_support',
      ],
      [BillingPlan.BASIC]: [
        'basic_loyalty',
        'customer_management',
        'mobile_app',
        'basic_analytics',
        'email_support',
        'advanced_rewards',
        'marketing_campaigns',
        'multi_location',
      ],
      [BillingPlan.PRO]: [
        'basic_loyalty',
        'customer_management',
        'mobile_app',
        'basic_analytics',
        'email_support',
        'advanced_rewards',
        'marketing_campaigns',
        'multi_location',
        'advanced_analytics',
        'api_access',
        'custom_branding',
        'priority_support',
      ],
      [BillingPlan.ENTERPRISE]: [
        'basic_loyalty',
        'customer_management',
        'mobile_app',
        'basic_analytics',
        'email_support',
        'advanced_rewards',
        'marketing_campaigns',
        'multi_location',
        'advanced_analytics',
        'api_access',
        'custom_branding',
        'priority_support',
        'advanced_security',
        'custom_integrations',
        'data_export',
      ],
    }

    return features[plan]
  }

  private calculatePeriodEnd(cycle: BillingCycle): DateTime {
    return cycle === BillingCycle.YEARLY
      ? DateTime.now().plus({ years: 1 })
      : DateTime.now().plus({ months: 1 })
  }
}
