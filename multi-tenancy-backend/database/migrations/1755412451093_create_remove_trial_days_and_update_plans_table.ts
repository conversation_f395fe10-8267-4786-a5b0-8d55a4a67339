import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'pricing_plans'

  async up() {
    // Remove trial_days column from pricing_plans table
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('trial_days')
    })

    // Delete BASIC and ENTERPRISE plans, keep only FREE and PRO
    this.defer(async (db) => {
      await db.from('pricing_plans').whereIn('slug', ['basic', 'enterprise']).delete()
    })
  }

  async down() {
    // Add back trial_days column
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('trial_days').defaultTo(0)
    })
  }
}
