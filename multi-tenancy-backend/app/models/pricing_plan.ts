import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { BillingCycle } from './billing.js'
import { BillingPlan } from '#constants/plan_limitations'

export interface PlanFeature {
  name: string
  description: string
  included: boolean
  limit?: number | null // For features with limits (e.g., API calls per month)
}

export interface PlanLimits {
  maxCustomers: number | null // null means unlimited
  maxLocations: number | null // Number of store locations, null means unlimited
  maxCampaigns: number | null // Marketing campaigns per month, null means unlimited
  maxRewards: number | null // Number of reward types, null means unlimited
}

export default class PricingPlan extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare slug: BillingPlan

  @column()
  declare description: string

  @column()
  declare monthlyPrice: number // Price in cents

  @column()
  declare yearlyPrice: number // Price in cents

  @column()
  declare currency: string

  @column()
  declare isPopular: boolean

  @column()
  declare isActive: boolean

  @column()
  declare sortOrder: number

  @column()
  declare stripePriceIdMonthly: string | null

  @column()
  declare stripePriceIdYearly: string | null

  @column()
  declare features: string // JSON string of PlanFeature[]

  @column()
  declare limits: string // JSON string of PlanLimits

  @column()
  declare trialDays: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Helper methods
  get featuresArray(): PlanFeature[] {
    if (!this.features) return []
    try {
      return JSON.parse(this.features)
    } catch {
      return []
    }
  }

  set featuresArray(features: PlanFeature[]) {
    this.features = JSON.stringify(features)
  }

  get limitsObject(): PlanLimits {
    if (!this.limits) {
      return {
        maxCustomers: null,
        maxLocations: null,
        maxCampaigns: null,
        maxRewards: null,
      }
    }
    try {
      return JSON.parse(this.limits)
    } catch {
      return {
        maxCustomers: null,
        maxLocations: null,
        maxCampaigns: null,
        maxRewards: null,
      }
    }
  }

  set limitsObject(limits: PlanLimits) {
    this.limits = JSON.stringify(limits)
  }

  getPrice(cycle: BillingCycle): number {
    return cycle === BillingCycle.YEARLY ? this.yearlyPrice : this.monthlyPrice
  }

  getStripePriceId(cycle: BillingCycle): string | null {
    return cycle === BillingCycle.YEARLY ? this.stripePriceIdYearly : this.stripePriceIdMonthly
  }

  hasFeature(featureName: string): boolean {
    return this.featuresArray.some((feature) => feature.name === featureName && feature.included)
  }

  getFeatureLimit(featureName: string): number | null {
    const feature = this.featuresArray.find((f) => f.name === featureName && f.included)
    return feature?.limit || null
  }

  get yearlySavingsPercentage(): number {
    if (this.monthlyPrice === 0) return 0
    const yearlyEquivalent = this.monthlyPrice * 12
    if (yearlyEquivalent === 0) return 0
    return Math.round(((yearlyEquivalent - this.yearlyPrice) / yearlyEquivalent) * 100)
  }

  get isUnlimitedCustomers(): boolean {
    return this.limitsObject.maxCustomers === null
  }

  get isUnlimitedLocations(): boolean {
    return this.limitsObject.maxLocations === null
  }

  get isUnlimitedCampaigns(): boolean {
    return this.limitsObject.maxCampaigns === null
  }

  get isUnlimitedRewards(): boolean {
    return this.limitsObject.maxRewards === null
  }
}
