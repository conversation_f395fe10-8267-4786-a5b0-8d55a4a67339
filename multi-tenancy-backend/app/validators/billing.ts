import vine from '@vinejs/vine'
import { BillingCycle } from '#models/billing'
import { BillingPlan } from '#constants/plan_limitations'
import { idValidation } from '#utils/validation'

/**
 * Validator for creating billing record
 */
export const createBillingValidator = vine.compile(
  vine.object({
    tenantId: idValidation,
    plan: vine.enum(Object.values(BillingPlan)),
    cycle: vine.enum(Object.values(BillingCycle)),
    amount: vine.number().optional(),
    currency: vine.string().optional(),
    maxUsers: vine.number().optional(),
    maxStorage: vine.number().optional(),
    features: vine.array(vine.string()).optional(),
    trialDays: vine.number().optional(),
  })
)

/**
 * Validator for updating billing record
 */
export const updateBillingValidator = vine.compile(
  vine.object({
    plan: vine.enum(Object.values(BillingPlan)).optional(),
    cycle: vine.enum(Object.values(BillingCycle)).optional(),
    amount: vine.number().optional(),
    currency: vine.string().optional(),
    maxUsers: vine.number().optional(),
    maxStorage: vine.number().optional(),
    features: vine.array(vine.string()).optional(),
    stripeCustomerId: vine.string().optional(),
    stripeSubscriptionId: vine.string().optional(),
    stripePriceId: vine.string().optional(),
  })
)
