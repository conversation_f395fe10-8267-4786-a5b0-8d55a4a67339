import { BaseSeeder } from '@adonisjs/lucid/seeders'
import { DateTime } from 'luxon'
import User, { UserStatus } from '#models/user'
import Tenant, { TenantStatus } from '#models/tenant'
import Role from '#models/role'
import UserRole from '#models/user_role'
import Billing, { BillingCycle, BillingStatus } from '#models/billing'
import { BillingPlan } from '#constants/plan_limitations'

const PASSWORD = '123'
export default class extends BaseSeeder {
  static environment = ['development', 'testing']

  async run() {
    // Create a default tenant for demo purposes

    const defaultTenant = await Tenant.firstOrCreate(
      { slug: 'demo-tenant' },
      {
        name: 'Demo Tenant',
        slug: 'demo-tenant',
        status: TenantStatus.ACTIVE,
        trialEndsAt: null, // No trial for demo tenant
      }
    )

    const secondTenant = await Tenant.firstOrCreate(
      { slug: 'second-tenant' },
      {
        name: 'Second Tenant',
        slug: 'second-tenant',
        status: TenantStatus.ACTIVE,
        trialEndsAt: null, // No trial for demo tenant
      }
    )

    // Create billing records for demo tenants if they don't exist
    await this.createBillingForTenant(defaultTenant.id)
    await this.createBillingForTenant(secondTenant.id)

    // Create super admin user
    const superAdmin = await User.firstOrCreate(
      { email: '<EMAIL>' },
      {
        firstName: 'Super',
        lastName: 'Admin',
        fullName: 'Super Admin',
        email: '<EMAIL>',
        password: PASSWORD,
        phone: '+1234567890',
        status: UserStatus.ACTIVE,
        emailVerifiedAt: DateTime.now(),
      }
    )

    // Assign super admin role
    const superAdminRole = await Role.findByOrFail('name', 'super_admin')

    await UserRole.firstOrCreate(
      { userId: superAdmin.id, roleId: superAdminRole.id, tenantId: null },
      { userId: superAdmin.id, roleId: superAdminRole.id, tenantId: null }
    )

    // Create a tenant owner for the demo tenant
    const tenantOwner = await User.firstOrCreate(
      { email: '<EMAIL>' },
      {
        firstName: 'Demo',
        lastName: 'Owner',
        fullName: 'Demo Owner',
        email: '<EMAIL>',
        password: PASSWORD,
        phone: '+1234567891',
        status: UserStatus.ACTIVE,
        emailVerifiedAt: DateTime.now(),
      }
    )

    // Assign tenant owner role
    const tenantOwnerRole = await Role.findByOrFail('name', 'tenant_owner')

    await UserRole.firstOrCreate(
      {
        userId: tenantOwner.id,
        roleId: tenantOwnerRole.id,
        tenantId: defaultTenant.id,
      },
      {
        userId: tenantOwner.id,
        roleId: tenantOwnerRole.id,
        tenantId: defaultTenant.id,
      }
    )

    // Create a tenant admin for the demo tenant
    const tenantAdmin = await User.firstOrCreate(
      { email: '<EMAIL>' },
      {
        firstName: 'Demo',
        lastName: 'Admin',
        fullName: 'Demo Admin',
        email: '<EMAIL>',
        password: PASSWORD,
        phone: '+1234567892',
        status: UserStatus.ACTIVE,
        emailVerifiedAt: DateTime.now(),
      }
    )

    // Assign tenant admin role
    const tenantAdminRole = await Role.findByOrFail('name', 'tenant_admin')
    await UserRole.firstOrCreate(
      {
        userId: tenantAdmin.id,
        roleId: tenantAdminRole.id,
        tenantId: defaultTenant.id,
      },
      {
        userId: tenantAdmin.id,
        roleId: tenantAdminRole.id,
        tenantId: defaultTenant.id,
      }
    )

    console.log('✅ Seeded users successfully!')
    console.log('📋 Created accounts:')
    console.log(`   🔑 Super Admin: ${superAdmin.email} `)
    console.log(`   👑 Tenant Owner: ${tenantOwner.email} `)
    console.log(`   👤 Tenant Admin: ${tenantAdmin.email} `)
    console.log(`   🏢 Demo Tenant: ${defaultTenant.name} (${defaultTenant.slug})`)
  }

  /**
   * Create billing record for a tenant if it doesn't exist
   */
  private async createBillingForTenant(tenantId: number): Promise<void> {
    const existingBilling = await Billing.query().where('tenant_id', tenantId).first()

    if (!existingBilling) {
      await Billing.create({
        tenantId,
        plan: BillingPlan.FREE,
        cycle: BillingCycle.MONTHLY,
        status: BillingStatus.ACTIVE,
        amount: 0,
        currency: 'usd',
        maxUsers: 3,
        maxStorage: 1024, // 1GB in MB
        features: JSON.stringify([
          'Basic Loyalty Program',
          'Customer Management',
          'Mobile App Access',
          'Basic Analytics',
        ]),
        cancelAtPeriodEnd: false,
        currentPeriodStart: DateTime.now(),
        currentPeriodEnd: DateTime.now().plus({ months: 1 }),
        needsUpgrade: false,
      })
      console.log(`✅ Created billing record for tenant ID: ${tenantId}`)
    }
  }
}
