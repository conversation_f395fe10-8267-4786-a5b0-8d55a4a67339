import { inject } from '@adonisjs/core'
import { PlanLimitationType, PlanLimitationAction } from '#enums/plan_limitation'
import { getPlanLimitation, isUnlimited } from '#constants/plan_limitations'
import UserRepository from '#repositories/user_repository'
import BadRequestException from '#exceptions/bad_request_exception'
import BillingRepository from '#repositories/billing_repository'

export interface PlanLimitationResult {
  allowed: boolean
  currentUsage: number
  limit: number | null
  remaining: number | null
  isUnlimited: boolean
  message?: string
}

export interface UsageStats {
  userCount: number
  storageUsed: number
  customersCount: number
  locationsCount: number
  campaignsCount: number
  rewardsCount: number
  apiCallsThisMonth: number
}

@inject()
export default class PlanLimitationService {
  constructor(
    private billingRepository: BillingRepository,

    private userRepository: UserRepository
  ) {}

  /**
   * Check if an action is allowed based on plan limitations
   */
  async checkActionAllowed(
    tenantId: number,
    action: PlanLimitationAction,
    quantity: number = 1
  ): Promise<PlanLimitationResult> {
    const billing = await this.billingRepository.findByTenantId(tenantId)
    if (!billing) {
      throw new BadRequestException('Billing information not found for tenant')
    }

    const limitationType = this.mapActionToLimitationType(action)
    const currentUsage = await this.getCurrentUsage(tenantId, limitationType)
    const limit = getPlanLimitation(billing.plan, limitationType)
    const unlimited = isUnlimited(billing.plan, limitationType)

    if (unlimited) {
      return {
        allowed: true,
        currentUsage,
        limit: null,
        remaining: null,
        isUnlimited: true,
      }
    }

    if (limit === null) {
      return {
        allowed: true,
        currentUsage,
        limit: null,
        remaining: null,
        isUnlimited: true,
      }
    }

    const wouldExceedLimit = currentUsage + quantity > limit
    const remaining = Math.max(0, limit - currentUsage)

    return {
      allowed: !wouldExceedLimit,
      currentUsage,
      limit,
      remaining,
      isUnlimited: false,
      message: wouldExceedLimit
        ? `Action would exceed plan limit. Current: ${currentUsage}, Limit: ${limit}, Requested: ${quantity}`
        : undefined,
    }
  }

  /**
   * Validate if an action can be performed, throw exception if not
   */
  async validateActionOrFail(
    tenantId: number,
    action: PlanLimitationAction,
    quantity: number = 1
  ): Promise<void> {
    const result = await this.checkActionAllowed(tenantId, action, quantity)
    if (!result.allowed) {
      throw new BadRequestException(
        result.message || `Plan limitation exceeded for action: ${action}`
      )
    }
  }

  /**
   * Get current usage for a specific limitation type
   */
  async getCurrentUsage(tenantId: number, limitationType: PlanLimitationType): Promise<number> {
    switch (limitationType) {
      case PlanLimitationType.USER_NUMBER:
        return await this.getUserCount(tenantId)
      case PlanLimitationType.STORAGE:
        return await this.getStorageUsage(tenantId)
      case PlanLimitationType.CUSTOMERS:
        return await this.getCustomersCount(tenantId)
      case PlanLimitationType.LOCATIONS:
        return await this.getLocationsCount(tenantId)
      case PlanLimitationType.CAMPAIGNS:
        return await this.getCampaignsCount(tenantId)
      case PlanLimitationType.REWARDS:
        return await this.getRewardsCount(tenantId)
      case PlanLimitationType.API_CALLS:
        return await this.getApiCallsThisMonth(tenantId)
      default:
        return 0
    }
  }

  /**
   * Get comprehensive usage statistics for a tenant
   */
  async getUsageStats(tenantId: number): Promise<UsageStats> {
    return {
      userCount: await this.getUserCount(tenantId),
      storageUsed: await this.getStorageUsage(tenantId),
      customersCount: await this.getCustomersCount(tenantId),
      locationsCount: await this.getLocationsCount(tenantId),
      campaignsCount: await this.getCampaignsCount(tenantId),
      rewardsCount: await this.getRewardsCount(tenantId),
      apiCallsThisMonth: await this.getApiCallsThisMonth(tenantId),
    }
  }

  /**
   * Get plan limitation status for all limitation types
   */
  async getPlanLimitationStatus(
    tenantId: number
  ): Promise<Record<PlanLimitationType, PlanLimitationResult>> {
    const billing = await this.billingRepository.findByTenantId(tenantId)
    if (!billing) {
      throw new BadRequestException('Billing information not found for tenant')
    }

    const result: Record<string, PlanLimitationResult> = {}

    for (const limitationType of Object.values(PlanLimitationType)) {
      const currentUsage = await this.getCurrentUsage(tenantId, limitationType)
      const limit = getPlanLimitation(billing.plan, limitationType)
      const unlimited = isUnlimited(billing.plan, limitationType)

      result[limitationType] = {
        allowed: unlimited || (limit !== null && currentUsage < limit),
        currentUsage,
        limit,
        remaining: unlimited || limit === null ? null : Math.max(0, limit - currentUsage),
        isUnlimited: unlimited,
      }
    }

    return result as Record<PlanLimitationType, PlanLimitationResult>
  }

  /**
   * Map action to limitation type
   */
  private mapActionToLimitationType(action: PlanLimitationAction): PlanLimitationType {
    const mapping = {
      [PlanLimitationAction.CREATE_USER]: PlanLimitationType.USER_NUMBER,
      [PlanLimitationAction.UPLOAD_FILE]: PlanLimitationType.STORAGE,
      [PlanLimitationAction.CREATE_CUSTOMER]: PlanLimitationType.CUSTOMERS,
      [PlanLimitationAction.CREATE_LOCATION]: PlanLimitationType.LOCATIONS,
      [PlanLimitationAction.CREATE_CAMPAIGN]: PlanLimitationType.CAMPAIGNS,
      [PlanLimitationAction.CREATE_REWARD]: PlanLimitationType.REWARDS,
      [PlanLimitationAction.API_REQUEST]: PlanLimitationType.API_CALLS,
    }

    return mapping[action]
  }

  /**
   * Get user count for tenant
   */
  private async getUserCount(tenantId: number): Promise<number> {
    const result = await this.userRepository.count({ tenantId })
    return result
  }

  /**
   * Get storage usage for tenant (placeholder - implement based on your storage system)
   */
  private async getStorageUsage(_tenantId: number): Promise<number> {
    // TODO: Implement based on your file storage system
    // This could query file uploads, attachments, etc.
    return 0
  }

  /**
   * Get customers count for tenant (placeholder - implement based on your customer model)
   */
  private async getCustomersCount(_tenantId: number): Promise<number> {
    // TODO: Implement based on your customer model
    return 0
  }

  /**
   * Get locations count for tenant (placeholder - implement based on your location model)
   */
  private async getLocationsCount(_tenantId: number): Promise<number> {
    // TODO: Implement based on your location model
    return 0
  }

  /**
   * Get campaigns count for tenant (placeholder - implement based on your campaign model)
   */
  private async getCampaignsCount(_tenantId: number): Promise<number> {
    // TODO: Implement based on your campaign model
    return 0
  }

  /**
   * Get rewards count for tenant (placeholder - implement based on your reward model)
   */
  private async getRewardsCount(_tenantId: number): Promise<number> {
    // TODO: Implement based on your reward model
    return 0
  }

  /**
   * Get API calls this month for tenant (placeholder - implement based on your API logging)
   */
  private async getApiCallsThisMonth(_tenantId: number): Promise<number> {
    // TODO: Implement based on your API logging system
    return 0
  }
}
