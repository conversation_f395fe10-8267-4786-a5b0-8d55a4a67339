import { BaseSeeder } from '@adonisjs/lucid/seeders'
import PricingPlan from '#models/pricing_plan'
import { BillingPlan } from '#constants/plan_limitations'
import Stripe from 'stripe'
import env from '#start/env'

export default class PricingPlanSeeder extends BaseSeeder {
  static environment = ['development', 'testing', 'production']

  private stripe: Stripe | null = null

  async run() {
    console.log('🔄 Creating pricing plans...')

    // Initialize Stripe if available
    await this.initializeStripe()

    // Define pricing plans data - simplified to just Free and Pro
    const pricingPlansData = [
      // FREE Plan
      {
        name: 'Free',
        slug: BillingPlan.FREE,
        description:
          'Perfect for getting started with basic loyalty features and limited restrictions',
        monthlyPrice: 0, // Price in cents
        yearlyPrice: 0,
        currency: 'usd',
        isPopular: false,
        isActive: true,
        sortOrder: 1,
        stripePriceIdMonthly: null,
        stripePriceIdYearly: null,
        features: JSON.stringify(this.getDefaultFeatures(BillingPlan.FREE)),
        limits: JSON.stringify(this.getDefaultLimits(BillingPlan.FREE)),
      },
      // PRO Plan
      {
        name: 'Pro',
        slug: BillingPlan.PRO,
        description: 'Full access to all features with unlimited usage',
        monthlyPrice: 4900, // $49.00 in cents
        yearlyPrice: 49000, // $490.00 in cents (10 months price)
        currency: 'usd',
        isPopular: true,
        isActive: true,
        sortOrder: 2,
        stripePriceIdMonthly: null, // Will be set after Stripe integration
        stripePriceIdYearly: null,
        features: JSON.stringify(this.getDefaultFeatures(BillingPlan.PRO)),
        limits: JSON.stringify(this.getDefaultLimits(BillingPlan.PRO)),
      },
    ]

    // Create or update pricing plans with Stripe integration
    for (const planData of pricingPlansData) {
      console.log(`🔄 Processing plan: ${planData.name}`)

      // Create or get Stripe product and prices if Stripe is available
      if (this.stripe) {
        await this.createOrGetStripeData(planData)
      }

      const existingPlan = await PricingPlan.query().where('slug', planData.slug).first()

      if (!existingPlan) {
        await PricingPlan.create(planData)
        console.log(`✅ Created pricing plan: ${planData.slug}`)
      } else {
        // Update existing plan with new data
        await existingPlan.merge(planData).save()
        console.log(`✅ Updated pricing plan: ${planData.slug}`)
      }
    }

    console.log('✅ Pricing plans seeding completed!')
  }

  private async initializeStripe(): Promise<void> {
    try {
      const stripeSecretKey = env.get('STRIPE_SECRET_KEY')
      if (!stripeSecretKey) {
        console.log('⚠️  STRIPE_SECRET_KEY not found - skipping Stripe integration')
        return
      }

      this.stripe = new Stripe(stripeSecretKey, {
        apiVersion: '2025-02-24.acacia',
      })

      console.log(`🔑 Stripe initialized with key: ${stripeSecretKey.substring(0, 12)}...`)
    } catch (error: any) {
      console.log(`⚠️  Failed to initialize Stripe: ${error.message}`)
      this.stripe = null
    }
  }

  private async createOrGetStripeData(planData: any): Promise<void> {
    if (!this.stripe) return

    try {
      // Create or get Stripe product
      const product = await this.createOrGetStripeProduct(planData)

      // Create or get monthly price if plan has monthly pricing
      if (planData.monthlyPrice > 0) {
        const monthlyPrice = await this.createOrGetStripePrice(
          product.id,
          planData.monthlyPrice,
          'month',
          `${planData.name} Monthly`
        )
        planData.stripePriceIdMonthly = monthlyPrice.id
      }

      // Create or get yearly price if plan has yearly pricing
      if (planData.yearlyPrice > 0) {
        const yearlyPrice = await this.createOrGetStripePrice(
          product.id,
          planData.yearlyPrice,
          'year',
          `${planData.name} Yearly`
        )
        planData.stripePriceIdYearly = yearlyPrice.id
      }

      console.log(`   ✅ Stripe data ready for ${planData.name}`)
    } catch (error: any) {
      console.log(`   ⚠️  Stripe integration failed for ${planData.name}: ${error.message}`)
    }
  }

  private async createOrGetStripeProduct(planData: any): Promise<Stripe.Product> {
    if (!this.stripe) throw new Error('Stripe not initialized')

    try {
      // First, try to find existing product by name
      const existingProducts = await this.stripe.products.list({
        limit: 100,
      })

      const existingProduct = existingProducts.data.find(
        (product) => product.name === planData.name && product.active
      )

      if (existingProduct) {
        console.log(
          `   🔍 Found existing Stripe product: ${existingProduct.id} (${existingProduct.name})`
        )
        return existingProduct
      }

      // Create new product if not found
      const product = await this.stripe.products.create({
        name: planData.name,
        description: planData.description,
        metadata: {
          plan_slug: planData.slug,
          created_by: 'pricing_plan_seeder',
        },
      })

      console.log(`   ✅ Created new Stripe product: ${product.id} (${product.name})`)
      return product
    } catch (error: any) {
      throw new Error(`Failed to create/get Stripe product: ${error.message}`)
    }
  }

  private async createOrGetStripePrice(
    productId: string,
    unitAmount: number,
    interval: 'month' | 'year',
    nickname: string
  ): Promise<Stripe.Price> {
    if (!this.stripe) throw new Error('Stripe not initialized')

    try {
      // First, try to find existing price
      const existingPrices = await this.stripe.prices.list({
        product: productId,
        limit: 100,
      })

      const existingPrice = existingPrices.data.find(
        (price) =>
          price.unit_amount === unitAmount && price.recurring?.interval === interval && price.active
      )

      if (existingPrice) {
        console.log(`   🔍 Found existing Stripe price: ${existingPrice.id} (${nickname})`)
        return existingPrice
      }

      // Create new price if not found
      const price = await this.stripe.prices.create({
        product: productId,
        unit_amount: unitAmount,
        currency: 'usd',
        recurring: {
          interval: interval,
        },
        nickname: nickname,
        metadata: {
          created_by: 'pricing_plan_seeder',
        },
      })

      console.log(`   ✅ Created new Stripe price: ${price.id} (${nickname})`)
      return price
    } catch (error: any) {
      throw new Error(`Failed to create/get Stripe price: ${error.message}`)
    }
  }

  private getDefaultLimits(plan: BillingPlan) {
    const limits = {
      [BillingPlan.FREE]: {
        maxCustomers: 100,
        maxLocations: 1,
        maxCampaigns: 2,
        maxRewards: 5,
      },
      [BillingPlan.PRO]: {
        maxCustomers: null, // Unlimited
        maxLocations: null, // Unlimited
        maxCampaigns: null, // Unlimited
        maxRewards: null, // Unlimited
      },
    }

    return limits[plan]
  }

  private getDefaultFeatures(plan: BillingPlan) {
    const features = {
      [BillingPlan.FREE]: [
        {
          name: 'Basic Loyalty Program',
          description: 'Simple points-based rewards',
          included: true,
        },
        { name: 'Customer Management', description: 'Basic customer profiles', included: true },
        { name: 'Mobile App Access', description: 'Customer mobile app', included: true },
        { name: 'Basic Analytics', description: 'Simple reports and insights', included: true },
        { name: 'Email Support', description: 'Standard email support', included: true },
        { name: 'Advanced Rewards', description: 'Tier-based rewards system', included: false },
        { name: 'Marketing Campaigns', description: 'Automated marketing tools', included: false },
        { name: 'Multi-Location', description: 'Multiple store locations', included: false },
        { name: 'API Access', description: 'REST API integration', included: false },
        {
          name: 'Advanced Analytics',
          description: 'Detailed reports and insights',
          included: false,
        },
        { name: 'Custom Branding', description: 'White-label customization', included: false },
        {
          name: 'Priority Support',
          description: 'Priority email and chat support',
          included: false,
        },
      ],
      [BillingPlan.PRO]: [
        {
          name: 'Basic Loyalty Program',
          description: 'Simple points-based rewards',
          included: true,
        },
        { name: 'Customer Management', description: 'Advanced customer profiles', included: true },
        { name: 'Mobile App Access', description: 'Customer mobile app', included: true },
        { name: 'Basic Analytics', description: 'Simple reports and insights', included: true },
        { name: 'Email Support', description: 'Standard email support', included: true },
        { name: 'Advanced Rewards', description: 'Tier-based rewards system', included: true },
        {
          name: 'Marketing Campaigns',
          description: 'Automated marketing tools',
          included: true,
        },
        {
          name: 'Multi-Location',
          description: 'Multiple store locations',
          included: true,
        },
        { name: 'API Access', description: 'REST API integration', included: true },
        {
          name: 'Advanced Analytics',
          description: 'Detailed reports and insights',
          included: true,
        },
        { name: 'Custom Branding', description: 'White-label customization', included: true },
        {
          name: 'Priority Support',
          description: 'Priority email and chat support',
          included: true,
        },
      ],
    }

    return features[plan]
  }
}
