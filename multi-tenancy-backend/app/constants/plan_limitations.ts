import { PlanLimitationType } from '#enums/plan_limitation'

export enum BillingPlan {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  ENTERPRISE = 'enterprise',
}

export interface PlanLimitationConfig {
  [key: string]: number | null // null means unlimited
}

export interface PlanLimitationsMap {
  [BillingPlan.FREE]: PlanLimitationConfig
  [BillingPlan.BASIC]: PlanLimitationConfig
  [BillingPlan.PRO]: PlanLimitationConfig
  [BillingPlan.ENTERPRISE]: PlanLimitationConfig
}

/**
 * Default plan limitations configuration
 * null means unlimited
 */
export const PLAN_LIMITATIONS: PlanLimitationsMap = {
  [BillingPlan.FREE]: {
    [PlanLimitationType.USER_NUMBER]: 2,
    [PlanLimitationType.STORAGE]: 1, // 1GB
    [PlanLimitationType.CUSTOMERS]: 100,
    [PlanLimitationType.LOCATIONS]: 1,
    [PlanLimitationType.CAMPAIGNS]: 2,
    [PlanLimitationType.REWARDS]: 5,
    [PlanLimitationType.API_CALLS]: 1000, // per month
  },
  [BillingPlan.BASIC]: {
    [PlanLimitationType.USER_NUMBER]: 5,
    [PlanLimitationType.STORAGE]: 5, // 5GB
    [PlanLimitationType.CUSTOMERS]: 1000,
    [PlanLimitationType.LOCATIONS]: 3,
    [PlanLimitationType.CAMPAIGNS]: 10,
    [PlanLimitationType.REWARDS]: 20,
    [PlanLimitationType.API_CALLS]: 10000, // per month
  },
  [BillingPlan.PRO]: {
    [PlanLimitationType.USER_NUMBER]: 15,
    [PlanLimitationType.STORAGE]: 25, // 25GB
    [PlanLimitationType.CUSTOMERS]: 10000,
    [PlanLimitationType.LOCATIONS]: 10,
    [PlanLimitationType.CAMPAIGNS]: 50,
    [PlanLimitationType.REWARDS]: 100,
    [PlanLimitationType.API_CALLS]: 100000, // per month
  },
  [BillingPlan.ENTERPRISE]: {
    [PlanLimitationType.USER_NUMBER]: null, // Unlimited
    [PlanLimitationType.STORAGE]: null, // Unlimited
    [PlanLimitationType.CUSTOMERS]: null, // Unlimited
    [PlanLimitationType.LOCATIONS]: null, // Unlimited
    [PlanLimitationType.CAMPAIGNS]: null, // Unlimited
    [PlanLimitationType.REWARDS]: null, // Unlimited
    [PlanLimitationType.API_CALLS]: null, // Unlimited
  },
}

/**
 * Get plan limitation for a specific plan and limitation type
 */
export function getPlanLimitation(
  plan: BillingPlan,
  limitationType: PlanLimitationType
): number | null {
  return PLAN_LIMITATIONS[plan][limitationType] || null
}

/**
 * Check if a plan has unlimited access for a specific limitation type
 */
export function isUnlimited(plan: BillingPlan, limitationType: PlanLimitationType): boolean {
  return getPlanLimitation(plan, limitationType) === null
}

/**
 * Get all limitations for a specific plan
 */
export function getPlanLimitations(plan: BillingPlan): PlanLimitationConfig {
  return PLAN_LIMITATIONS[plan]
}
